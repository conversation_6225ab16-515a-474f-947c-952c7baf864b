"use client";

import {
  Label,
  PolarGrid,
  PolarRadiusAxis,
  RadialBar,
  RadialBarChart,
} from "recharts";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { ChartConfig, ChartContainer } from "@/components/ui/chart";
import { calculatePercentage, getTotalStorage, getFileSize } from "@/lib/utils";
import { FileInfo } from "@/app/types";

const chartConfig = {
  storage: {
    label: "Storage Used",
    color: "hsl(var(--chart-1))",
  },
} satisfies ChartConfig;

export const StorageChart = ({ files }: { files: FileInfo[] }) => {
  // getTotalStorage should return a number (bytes)
  const used = getTotalStorage(files); // bytes
  const size = getFileSize(used); // formatted string
  const percentage = calculatePercentage(used);

  // Ensure we have valid data for the chart
  const chartData = [
    {
      storage: "used",
      value: percentage,
      fill: "white",
    },
  ];

  // Calculate angles for the radial chart
  const startAngle = 90;
  const endAngle = startAngle + percentage * 3.6; // Convert percentage to degrees (360 degrees = 100%)

  return (
    <Card className="chart">
      <CardContent className="flex-1 p-0">
        <ChartContainer config={chartConfig} className="chart-container">
          <RadialBarChart
            data={chartData}
            startAngle={startAngle}
            endAngle={endAngle}
            innerRadius={80}
            outerRadius={110}
            width={200}
            height={200}
          >
            <PolarGrid
              gridType="circle"
              radialLines={false}
              stroke="none"
              className="polar-grid"
              polarRadius={[86, 74]}
            />
            <RadialBar
              dataKey="value"
              background={{ fill: "rgba(255, 255, 255, 0.1)" }}
              cornerRadius={10}
              fill="white"
            />
            <PolarRadiusAxis tick={false} tickLine={false} axisLine={false}>
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="chart-total-percentage"
                        >
                          {percentage}%
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-white/70"
                        >
                          Space used
                        </tspan>
                      </text>
                    );
                  }
                }}
              />
            </PolarRadiusAxis>
          </RadialBarChart>
        </ChartContainer>
      </CardContent>
      <CardHeader className="chart-details">
        <CardTitle className="chart-title">Available Storage</CardTitle>
        <CardDescription className="chart-description">
          {size} / 2GB
        </CardDescription>
      </CardHeader>
    </Card>
  );
};
