import { fileType } from "@/app/types";
import Image from "next/image";
import { getFileIcon, getFileColor } from "@/lib/utils";
import { FileInfo } from "@/app/types";
import { getFileSize } from "@/lib/utils";

const DashboardCard = ({
  type,
  files,
}: {
  type: fileType;
  files: FileInfo[];
}) => {
  const fileIcon = getFileIcon({ type });
  const fileColor = getFileColor(type);
  const filteredFiles = files.filter((file) => file.type === type);
  const fileSize = filteredFiles.reduce((acc, file) => acc + file.size, 0);
  const size = getFileSize(fileSize);
  const fileCount = filteredFiles.length;

  // Get the most recent file for "last updated"
  const mostRecentFile = filteredFiles.sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
  )[0];

  const lastUpdated = mostRecentFile
    ? new Date(mostRecentFile.createdAt).toLocaleDateString()
    : "No files";

  const typeLabels = {
    images: "Images",
    videos: "Videos",
    documents: "Documents",
    others: "Other Files",
  };

  return (
    <div className="group relative overflow-hidden rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-all duration-300 hover:border-gray-300 hover:shadow-md">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white opacity-50"></div>

      {/* Content */}
      <div className="relative z-10">
        {/* Icon and file count */}
        <div className="mb-4 flex items-start justify-between">
          <div
            className={`flex h-12 w-12 items-center justify-center rounded-lg ${fileColor} transition-transform duration-300 group-hover:scale-110`}
          >
            <Image src={fileIcon} alt={`${type}-icon`} width={24} height={24} />
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">{fileCount}</div>
            <div className="text-xs text-gray-500">files</div>
          </div>
        </div>

        {/* File type and size */}
        <div className="mb-3">
          <h3 className="text-lg font-semibold text-gray-900 capitalize">
            {typeLabels[type]}
          </h3>
          <p className="text-sm font-medium text-gray-600">{size}</p>
        </div>

        {/* Last updated */}
        <div className="text-xs text-gray-500">
          <span>Last updated: {lastUpdated}</span>
        </div>

        {/* Progress bar for visual appeal */}
        <div className="mt-4">
          <div className="h-1 w-full rounded-full bg-gray-200">
            <div
              className={`h-1 rounded-full transition-all duration-500 ${
                type === "images"
                  ? "bg-blue-500"
                  : type === "videos"
                    ? "bg-red-500"
                    : type === "documents"
                      ? "bg-green-500"
                      : "bg-purple-500"
              }`}
              style={{
                width:
                  fileCount > 0
                    ? `${Math.min((fileCount / Math.max(files.length, 1)) * 100, 100)}%`
                    : "0%",
              }}
            ></div>
          </div>
        </div>
      </div>

      {/* Hover effect overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent to-gray-50 opacity-0 transition-opacity duration-300 group-hover:opacity-30"></div>
    </div>
  );
};

export default DashboardCard;
