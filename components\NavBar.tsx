"use client";
import { useId } from "react";
import { SearchIcon } from "lucide-react";

import NotificationMenu from "@/components/notification-menu";
import UserMenu from "@/components/user-menu";
import { Input } from "@/components/ui/input";
import { SidebarTrigger } from "@/components/ui/sidebar";

// Navigation links array to be used in both desktop and mobile menus

export default function NavBar() {
  const id = useId();

  return (
    <header className="border-b border-gray-200 bg-white/80 px-4 backdrop-blur-md md:px-6">
      <div className="flex h-16 items-center justify-between gap-4">
        <SidebarTrigger className="text-gray-600 hover:text-gray-900" />

        {/* Left side */}
        <div className="flex flex-1 items-center gap-2">
          {/* Mobile menu trigger */}
        </div>

        {/* Middle area - Search */}
        <div className="grow">
          <div className="relative mx-auto w-full max-w-xs">
            <Input
              id={id}
              className="peer h-9 border-gray-300 ps-8 pe-10 focus:border-blue-500 focus:ring-blue-500"
              placeholder="Search files..."
              type="search"
            />
            <div className="pointer-events-none absolute inset-y-0 start-0 flex items-center justify-center ps-2 text-gray-400 peer-disabled:opacity-50">
              <SearchIcon size={16} />
            </div>
            <div className="pointer-events-none absolute inset-y-0 end-0 flex items-center justify-center pe-2 text-gray-400">
              <kbd className="inline-flex h-5 max-h-full items-center rounded border border-gray-300 bg-gray-50 px-1 font-[inherit] text-[0.625rem] font-medium text-gray-500">
                ⌘K
              </kbd>
            </div>
          </div>
        </div>

        {/* Right side */}
        <div className="flex flex-1 items-center justify-end gap-3">
          <NotificationMenu />
          <UserMenu />
        </div>
      </div>
    </header>
  );
}
