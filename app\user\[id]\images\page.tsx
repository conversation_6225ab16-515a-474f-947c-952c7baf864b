import GetFiles from "@/components/get-files";
import PageTitle from "@/components/page-title";
import FileUpload from "@/components/file-upload";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { listFiles } from "@/app/actions/userActions";
import { getTotalStorage, getFileSize } from "@/lib/utils";

const page = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const userId = session?.user?.id;
  const files = await listFiles(userId);
  const imageFiles = files.filter((file) => file.type === "images");
  const totalSize = getTotalStorage(imageFiles);
  const formattedSize = getFileSize(totalSize);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 md:p-6 lg:p-8">
      <div className="mx-auto max-w-7xl">
        {/* Header Section */}
        <div className="mb-8">
          <div className="mb-4 flex items-center gap-4">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 shadow-lg">
              <svg
                className="h-8 w-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Images</h1>
              <p className="text-gray-600">
                Manage and organize your image files
              </p>
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>{imageFiles.length} images</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-indigo-500"></div>
              <span>{formattedSize} total size</span>
            </div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-4">
          {/* File Upload Section */}
          <div className="lg:col-span-1">
            <div className="sticky top-8">
              <div className="rounded-2xl border border-gray-200 bg-white p-6 shadow-sm">
                <h2 className="mb-4 text-lg font-semibold text-gray-900">
                  Upload Images
                </h2>
                <FileUpload type="images" />
              </div>
            </div>
          </div>

          {/* Files Grid */}
          <div className="lg:col-span-3">
            <div className="rounded-2xl border border-gray-200 bg-white p-6 shadow-sm">
              <PageTitle
                title="Your Images"
                size={parseFloat(formattedSize.replace(/[^\d.]/g, ""))}
              />
              <div className="mt-6">
                <GetFiles type="images" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
