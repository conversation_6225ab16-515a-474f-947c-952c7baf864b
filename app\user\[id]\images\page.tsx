import GetFiles from "@/components/get-files";
import PageTitle from "@/components/page-title";
import FileUpload from "@/components/file-upload";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { listFiles } from "@/app/actions/userActions";
import { getTotalStorage, getFileSize } from "@/lib/utils";

const page = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const userId = session?.user?.id;
  const files = await listFiles(userId);
  const imageFiles = files.filter((file) => file.type === "images");
  const totalSize = getTotalStorage(imageFiles);
  const formattedSize = getFileSize(totalSize);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl px-4 py-6 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="mb-6 flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-yellow-100">
              <svg
                className="h-6 w-6 text-yellow-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Images</h1>
              <p className="text-sm text-gray-600">
                {imageFiles.length} files • {formattedSize}
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="grid grid-cols-1 gap-6 xl:grid-cols-5">
          {/* Upload Section */}
          <div className="xl:col-span-2">
            <div className="rounded-lg border border-gray-200 bg-white p-6">
              <h2 className="mb-4 text-lg font-medium text-gray-900">
                Upload Images
              </h2>
              <FileUpload type="images" />
            </div>
          </div>

          {/* Files Section */}
          <div className="xl:col-span-3">
            <div className="rounded-lg border border-gray-200 bg-white p-6">
              <div className="mb-6">
                <h2 className="text-lg font-medium text-gray-900">
                  Your Images
                </h2>
              </div>
              <GetFiles type="images" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
