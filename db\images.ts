// lib/db/images.ts
import { db } from "@/db";
import { images } from "./schema";
import { eq } from "drizzle-orm";

export const insertUserImage = async (userId: string, url: string) => {
    return db.insert(images).values({
        id: crypto.randomUUID(),
        userId,
        url,
    }).returning();
};

export const getUserImages = async (userId: string) => {
    return db.select()
        .from(images)
        .where(eq(images.userId, userId));
};