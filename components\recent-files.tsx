import Image from "next/image";
import { FileInfo } from "@/app/types";
import {
  getFileIcon,
  getFileColor,
  getFileSize,
  getFileIconColor,
} from "@/lib/utils";

const RecentFiles = ({ file }: { file: FileInfo }) => {
  const icon = getFileIcon({ type: file.type });
  const fileColor = getFileColor(file.type);
  const iconColor = getFileIconColor(file.type);
  const formattedSize = getFileSize(file.size);
  const formattedDate = new Date(file.createdAt).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
  return (
    <div className="group flex items-center gap-3 rounded-lg border border-gray-100 p-3 transition-all duration-200 hover:border-gray-200 hover:bg-gray-50">
      {/* File icon */}
      <div
        className={`flex h-10 w-10 items-center justify-center rounded-lg ${fileColor} transition-transform duration-200 group-hover:scale-105`}
      >
        <Image src={icon} alt="file-icon" width={20} height={20} className="" />
      </div>

      {/* File details */}
      <div className="min-w-0 flex-1">
        <div className="flex items-center justify-between">
          <p className="truncate text-sm font-medium text-gray-900">
            {file.name.length > 25 ? `${file.name.slice(0, 25)}...` : file.name}
          </p>
          <span className="ml-2 flex-shrink-0 text-xs text-gray-500">
            {formattedSize}
          </span>
        </div>
        <div className="mt-1 flex items-center justify-between">
          <span className="text-xs text-gray-500 capitalize">{file.type}</span>
          <span className="text-xs text-gray-400">{formattedDate}</span>
        </div>
      </div>
    </div>
  );
};

export default RecentFiles;
