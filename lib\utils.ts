import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { fileType } from "@/app/types"
import { FileInfo } from "@/app/types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const parseStringify = (value: unknown) => {
  return JSON.parse(JSON.stringify(value))
}

export const getFileType = (extension: string) => {

  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  const videoExtensions = ['mp4', 'avi', 'mov', 'mkv'];
  const audioExtensions = ['mp3', 'wav', 'ogg'];
  const documentExtensions = ['pdf', 'docx', 'txt', 'pptx'];

  if (imageExtensions.includes(extension)) return 'images';
  if (videoExtensions.includes(extension)) return 'videos';
  if (audioExtensions.includes(extension)) return 'others';
  if (documentExtensions.includes(extension)) return 'documents';

  // If not matched, return 'others' for unknown/other types
  return 'others';
}

export const getFileIcon = ({ type }: { type: fileType | undefined }) => {
  switch (type) {
    case 'images':
      return '/assets/icons/file-image.svg'; // Image icon
    case 'videos':
      return '/assets/icons/file-video.svg'; // Video icon
    case 'documents':
      return '/assets/icons/file-doc.svg'; // Document icon
    default:
      return '/assets/icons/file-other.svg'; // Default file icon for other types
  }
}

export const getFileIconColor = (type: string | undefined) => {
  switch (type) {
    case "images":
      return "text-yellow-600";
    case "videos":
      return "text-red-600";
    case "documents":
      return "text-purple-600";
    case "others":
      return "text-blue-600";
    default:
      return "text-gray-600";
  }
};

export const getFileColor = (type: string | undefined) => {
  switch (type) {
    case "images":
      return "bg-yellow-100";
    case "videos":
      return "bg-red-100";
    case "documents":
      return "bg-purple-100";
    case "others":
      return "bg-blue-100";
    default:
      return "bg-gray-100";
  }
};

export const getFileSize = (size: number) => {
  if (size < 1024) return `${size} B`;

  const sizeInMB = size / (1024 * 1024);

  // If less than 1 MB, show in KB
  if (sizeInMB < 1) {
    const sizeInKB = size / 1024;
    return `${sizeInKB.toFixed(1)} KB`;
  }

  // If less than 1 GB, show in MB
  if (sizeInMB < 1024) {
    return `${sizeInMB.toFixed(1)} MB`;
  }

  // If 1 GB or more, show in GB
  const sizeInGB = sizeInMB / 1024;
  return `${sizeInGB.toFixed(1)} GB`;
};

export const getTotalStorage = (files: FileInfo[]) => {
  const totalSize = files.reduce((acc, file) => acc + file.size, 0);
  return totalSize;
};

export const calculatePercentage = (sizeInBytes: number) => {
  const totalSizeInBytes = 2 * 1024 * 1024 * 1024; // 2GB in bytes
  const percentage = (sizeInBytes / totalSizeInBytes) * 100;
  return Number(percentage.toFixed(2));
};

