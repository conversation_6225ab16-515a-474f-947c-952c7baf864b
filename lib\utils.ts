import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { fileType } from "@/app/types"
import { FileInfo } from "@/app/types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export const parseStringify = (value: unknown) => {
  return JSON.parse(JSON.stringify(value))
}

export const getFileType = (extension: string) => {

  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  const videoExtensions = ['mp4', 'avi', 'mov', 'mkv'];
  const audioExtensions = ['mp3', 'wav', 'ogg'];
  const documentExtensions = ['pdf', 'docx', 'txt', 'pptx'];

  if (imageExtensions.includes(extension)) return 'images';
  if (videoExtensions.includes(extension)) return 'videos';
  if (audioExtensions.includes(extension)) return 'others';
  if (documentExtensions.includes(extension)) return 'documents';

  // If not matched, return 'others' for unknown/other types
  return 'others';
}

export const getFileIcon = ({ type }: { type: fileType | undefined }) => {
  switch (type) {
    case 'images':
      return '/assets/icons/file-image.svg'; // Image icon
    case 'videos':
      return '/assets/icons/file-video.svg'; // Video icon
    case 'documents':
      return '/assets/icons/file-doc.svg'; // Document icon
    default:
      return '/assets/icons/file-other.svg'; // Default file icon for other types
  }
}

export const getFileIconColor = (type: string | undefined) => {
  switch (type) {
    case "images":
      return "text-blue-600";
    case "videos":
      return "text-red-600";
    case "documents":
      return "text-green-600";
    case "others":
      return "text-purple-600";
    default:
      return "text-gray-600";
  }
};

export const getFileColor = (type: string | undefined) => {
  switch (type) {
    case "images":
      return "bg-yellow-100";
    case "videos":
      return "bg-red-100";
    case "documents":
      return "bg-green-100";
    case "others":
      return "bg-purple-100";
    default:
      return "bg-gray-100";
  }
};

export const getFileSize = (size: number) => {
  if (size < 1024) return `${size} B`;
  const units = ["B", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(size) / Math.log(1024));
  return `${(size / Math.pow(1024, i)).toFixed(2)} ${units[i]}`;
};

export const getTotalStorage = (files: FileInfo[]) => {
  const totalSize = files.reduce((acc, file) => acc + file.size, 0);
  return totalSize;
};

export const calculatePercentage = (sizeInBytes: number) => {
  const totalSizeInBytes = 2 * 1024 * 1024 * 1024; // 2GB in bytes
  const percentage = (sizeInBytes / totalSizeInBytes) * 100;
  return Number(percentage.toFixed(2));
};

