import { listFiles } from "@/app/actions/userActions";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import ListFiles from "./list-files";
import { fileType } from "@/app/types";
const GetFiles = async ({ type }: { type: fileType }) => {
  const session = await auth.api.getSession({ headers: await headers() });
  const initialFiles = (await listFiles(session!.user.id)).filter(
    (file) => file.type === type,
  );

  return (
    <ListFiles
      initialFiles={initialFiles}
      userId={session!.user.id}
      fileType={type}
    />
  );
};
export default GetFiles;
