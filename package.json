{"name": "next_storage", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "npx drizzle-kit push", "db:pull": "npx drizzle-kit pull", "db:migrate": "npx drizzle-kit migrate", "db:seed": "npx drizzle-kit seed", "db:console": "npx drizzle-kit console", "db:studio": "npx drizzle-kit studio"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@supabase/supabase-js": "^2.49.8", "@tailwindcss/typography": "github:tailwindcss/typography", "add": "^2.0.6", "better-auth": "^1.2.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "firebase": "^11.9.1", "form": "^0.2.5", "framer-motion": "^12.23.3", "input-otp": "^1.4.2", "lucide-react": "^0.488.0", "next": "15.3.0", "next-themes": "^0.4.6", "radix-ui": "^1.4.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.55.0", "recharts": "^2.15.4", "shadcn": "^2.4.1", "sonner": "^2.0.3", "supabase": "^2.23.4", "tailwind-merge": "^3.2.0", "ts-key-enum": "^3.0.13", "tw-animate-css": "^1.2.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.4", "typescript": "^5"}}