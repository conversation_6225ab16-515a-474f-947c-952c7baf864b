import DashboardCard from "@/components/dashboard-card";
import ListRecentFiles from "@/components/list-recent-files";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { listFiles } from "@/app/actions/userActions";
import { StorageProgress } from "@/components/storage-progress";
import { getTotalStorage, getFileSize } from "@/lib/utils";

const page = async () => {
  const session = await auth.api.getSession({
    headers: await headers(),
  });
  const userId = session?.user?.id;
  const files = await listFiles(userId);
  const totalStorage = getTotalStorage(files);
  const formattedSize = getFileSize(totalStorage);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-gray-100 p-4 md:p-6 lg:p-8">
      <div className="mx-auto max-w-7xl">
        {/* Header Section */}
        <div className="mb-8">
          <div className="mb-4 flex items-center gap-4">
            <div className="flex h-16 w-16 items-center justify-center rounded-2xl bg-gradient-to-r from-blue-500 to-purple-600 shadow-lg">
              <svg
                className="h-8 w-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"
                />
              </svg>
            </div>
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-gray-600">
                Welcome back, {session?.user?.name || "User"}! Manage your files
                and monitor storage usage
              </p>
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600">
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-blue-500"></div>
              <span>{files.length} files</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-2 w-2 rounded-full bg-purple-500"></div>
              <span>{formattedSize} total size</span>
            </div>
          </div>
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 gap-8 xl:grid-cols-3">
          {/* Left Column - Storage Chart and File Type Cards */}
          <div className="space-y-8 xl:col-span-2">
            {/* Storage Progress Section */}
            <div>
              <StorageProgress files={files} />
            </div>

            {/* File Type Cards Grid */}
            <div className="space-y-6">
              <div className="text-center">
                <h2 className="mb-2 text-2xl font-bold text-gray-900">
                  File Categories
                </h2>
                <p className="text-gray-600">
                  Overview of your files organized by type
                </p>
              </div>
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
                <DashboardCard type="images" files={files} />
                <DashboardCard type="videos" files={files} />
                <DashboardCard type="documents" files={files} />
                <DashboardCard type="others" files={files} />
              </div>
            </div>
          </div>

          {/* Right Column - Recent Files */}
          <div className="xl:col-span-1">
            <div className="sticky top-8 h-fit rounded-2xl border border-gray-100 bg-white p-6 shadow-lg">
              <div className="mb-6">
                <h2 className="mb-2 text-xl font-semibold text-gray-900">
                  Recent Uploads
                </h2>
                <p className="text-sm text-gray-600">
                  Your latest uploaded files
                </p>
              </div>
              <div className="space-y-4">
                <ListRecentFiles files={files} />
              </div>
              {files.length === 0 && (
                <div className="py-12 text-center">
                  <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
                    <svg
                      className="h-8 w-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                  </div>
                  <h3 className="mb-2 text-lg font-medium text-gray-900">
                    No files yet
                  </h3>
                  <p className="text-sm text-gray-500">
                    Upload your first file to get started
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Actions Section */}
        <div className="mt-8 rounded-2xl border border-gray-100 bg-white p-6 shadow-lg">
          <div className="mb-6">
            <h2 className="mb-2 text-xl font-semibold text-gray-900">
              Quick Actions
            </h2>
            <p className="text-sm text-gray-600">
              Common tasks and file management options
            </p>
          </div>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <a
              href="/user/dashboard/images"
              className="group flex items-center gap-3 rounded-xl border border-gray-200 p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-blue-100 transition-colors group-hover:bg-blue-200">
                <svg
                  className="h-5 w-5 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">View Images</h3>
                <p className="text-sm text-gray-500">Browse image files</p>
              </div>
            </a>

            <a
              href="/user/dashboard/videos"
              className="group flex items-center gap-3 rounded-xl border border-gray-200 p-4 transition-all duration-200 hover:border-red-300 hover:bg-red-50"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-red-100 transition-colors group-hover:bg-red-200">
                <svg
                  className="h-5 w-5 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">View Videos</h3>
                <p className="text-sm text-gray-500">Browse video files</p>
              </div>
            </a>

            <a
              href="/user/dashboard/documents"
              className="group flex items-center gap-3 rounded-xl border border-gray-200 p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-green-100 transition-colors group-hover:bg-green-200">
                <svg
                  className="h-5 w-5 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">View Documents</h3>
                <p className="text-sm text-gray-500">Browse document files</p>
              </div>
            </a>

            <a
              href="/user/dashboard/others"
              className="group flex items-center gap-3 rounded-xl border border-gray-200 p-4 transition-all duration-200 hover:border-purple-300 hover:bg-purple-50"
            >
              <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-purple-100 transition-colors group-hover:bg-purple-200">
                <svg
                  className="h-5 w-5 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                  />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-gray-900">Other Files</h3>
                <p className="text-sm text-gray-500">Browse other file types</p>
              </div>
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
