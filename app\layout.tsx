import type { <PERSON>ada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import "./globals.css";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "SafeSpace - Secure File Storage & Management",
  description:
    "Store, organize, and access your files from anywhere. SafeSpace provides secure cloud storage with an intuitive interface that makes file management effortless.",
  keywords:
    "file storage, cloud storage, file management, secure storage, document management",
  authors: [{ name: "SafeSpace Team" }],
  viewport: "width=device-width, initial-scale=1",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={poppins.variable}>
      <body
        className={`${poppins.className} bg-white text-gray-900 antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
